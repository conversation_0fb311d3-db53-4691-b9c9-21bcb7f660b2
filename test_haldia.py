#!/usr/bin/env python3
"""
Test script specifically for Haldia Energy power station
to demonstrate the enhanced parent company detection.
"""

import asyncio
import json
import sys
from pathlib import Path

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from credit_rating_agent import CreditRatingAgent
from credit_rating_schemas import CreditRatingConfig
from utils import setup_logging


async def test_haldia_enhanced():
    """Test Haldia Energy with enhanced parent company detection."""
    print("🔍 Testing Enhanced Credit Rating Agent with Haldia Energy")
    print("=" * 60)
    
    # Setup logging
    setup_logging(verbose=True)
    
    try:
        config = CreditRatingConfig(
            max_search_duration=45,  # Longer duration for thorough search
            confidence_threshold=0.3,
            output_directory="./test_results"
        )
        
        agent = CreditRatingAgent(config)
        await agent.initialize()
        
        # Test with Haldia Energy
        test_plant = "Haldia Energy power station"
        print(f"🏭 Searching for: {test_plant}")
        print("📋 Enhanced features:")
        print("   • Dynamic parent company detection (no hard-coding)")
        print("   • PDF document processing capability")
        print("   • Intelligent ownership pattern matching")
        print()
        
        result = await agent.research_credit_rating(test_plant)
        
        print("📊 Results:")
        print(f"   • Plant: {result.power_plant_name}")
        print(f"   • Confidence: {result.confidence_score:.2f}")
        print(f"   • Level: {result.level.value}")
        print(f"   • Tier used: {result.search_metadata.tier_used.value}")
        print(f"   • Sources found: {result.search_metadata.sources_found}")
        print(f"   • Queries executed: {len(result.search_metadata.queries_executed)}")
        print(f"   • Duration: {result.search_metadata.search_duration_seconds:.1f}s")
        
        if result.search_metadata.parent_company_identified:
            print(f"   • Parent company identified: {result.search_metadata.parent_company_identified}")
            print(f"   • Proceeded to Tier 3: {'Yes' if result.search_metadata.tier_used.value == 3 else 'No'}")
        else:
            print("   • No parent company identified")
        
        print(f"   • Agencies found: {len(result.credit_rating)}")
        
        if result.credit_rating:
            print("   • Rating details:")
            for agency_rating in result.credit_rating:
                print(f"     - {agency_rating.name}: {len(agency_rating.yearwise_rating)} ratings")
                for rating in agency_rating.yearwise_rating[:2]:  # Show first 2
                    print(f"       * {rating.year}: {rating.rating}")
        
        print(f"\n📝 Note: {result.credit_rating_note}")
        
        # Save detailed result
        output_dir = Path("./test_results")
        output_dir.mkdir(exist_ok=True)
        
        output_file = output_dir / f"haldia_enhanced_test.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result.dict(), f, indent=2, default=str, ensure_ascii=False)
        
        print(f"\n💾 Detailed results saved to: {output_file}")
        
        # Show some of the queries that were executed
        print(f"\n🔍 Sample queries executed:")
        for i, query in enumerate(result.search_metadata.queries_executed[:5]):
            print(f"   {i+1}. {query}")
        if len(result.search_metadata.queries_executed) > 5:
            print(f"   ... and {len(result.search_metadata.queries_executed) - 5} more")
        
        await agent.cleanup()
        
        # Analysis
        print(f"\n📈 Analysis:")
        if result.search_metadata.tier_used.value == 1:
            print("   ✅ Found direct plant ratings (Tier 1)")
        elif result.search_metadata.tier_used.value == 2:
            print("   ⚠️  No direct ratings, stopped at corporate discovery (Tier 2)")
            print("   💡 This suggests no clear parent company was identified")
        elif result.search_metadata.tier_used.value == 3:
            print("   ✅ Found parent company, searched parent ratings (Tier 3)")
        
        if result.search_metadata.sources_found > 0:
            print(f"   📄 Found {result.search_metadata.sources_found} sources to analyze")
        else:
            print("   ❌ No sources found - this is unusual")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def test_document_processing():
    """Test document processing capabilities."""
    print("\n🔧 Testing Document Processing Capabilities")
    print("=" * 50)
    
    try:
        from document_processor import DocumentProcessor
        processor = DocumentProcessor()
        
        print(f"📄 PDF processing available: {processor.can_process_pdfs()}")
        
        if not processor.can_process_pdfs():
            print("💡 To enable PDF processing, install:")
            print("   pip install PyPDF2 pdfplumber requests")
        else:
            print("✅ PDF processing is ready")
            print("   • Can download and process PDF documents")
            print("   • Extracts text content for rating analysis")
            print("   • Identifies rating agencies and years")
        
        return True
        
    except Exception as e:
        print(f"❌ Document processing test failed: {str(e)}")
        return False


async def main():
    """Main test function."""
    print("🧪 Enhanced Credit Rating Agent - Haldia Energy Test")
    print("=" * 70)
    
    tests = [
        ("Document Processing", test_document_processing),
        ("Haldia Energy Enhanced Search", test_haldia_enhanced),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running: {test_name}")
        try:
            result = await test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} passed")
            else:
                print(f"❌ {test_name} failed")
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {str(e)}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed!")
        print("\n💡 Key improvements:")
        print("   • Dynamic parent company detection (no hard-coding)")
        print("   • Enhanced ownership pattern matching")
        print("   • PDF document processing capability")
        print("   • Better corporate structure discovery")
    else:
        print("\n⚠️  Some tests failed. Check the output above.")
    
    return passed == total


if __name__ == "__main__":
    asyncio.run(main())
