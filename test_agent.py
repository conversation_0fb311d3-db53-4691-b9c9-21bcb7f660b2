#!/usr/bin/env python3
"""
Test script for the Credit Rating Research Agent

This script validates the implementation and provides examples of usage.
"""

import asyncio
import json
import os
import sys
from pathlib import Path

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from credit_rating_agent import CreditRatingAgent
from credit_rating_schemas import CreditRatingConfig
from utils import validate_environment, setup_logging


async def test_environment():
    """Test environment setup and validation."""
    print("🔧 Testing environment setup...")
    
    validation = validate_environment()
    
    if validation["valid"]:
        print("✅ Environment validation passed")
    else:
        print("❌ Environment validation failed:")
        for error in validation["errors"]:
            print(f"   • {error}")
        return False
    
    if validation["warnings"]:
        print("⚠️  Warnings:")
        for warning in validation["warnings"]:
            print(f"   • {warning}")
    
    return True


async def test_agent_initialization():
    """Test agent initialization."""
    print("\n🤖 Testing agent initialization...")
    
    try:
        config = CreditRatingConfig(
            max_search_duration=10,  # Shorter for testing
            confidence_threshold=0.3,
            output_directory="./test_results"
        )
        
        agent = CreditRatingAgent(config)
        await agent.initialize()
        
        print("✅ Agent initialized successfully")
        await agent.cleanup()
        return True
        
    except Exception as e:
        print(f"❌ Agent initialization failed: {str(e)}")
        return False


async def test_single_search():
    """Test a single power plant search."""
    print("\n🔍 Testing single power plant search...")
    
    try:
        config = CreditRatingConfig(
            max_search_duration=15,
            confidence_threshold=0.3,
            output_directory="./test_results"
        )
        
        agent = CreditRatingAgent(config)
        await agent.initialize()
        
        # Test with a well-known power plant
        test_plant = "Adani Mundra Power Plant"
        print(f"   Searching for: {test_plant}")
        
        result = await agent.research_credit_rating(test_plant)
        
        print(f"✅ Search completed:")
        print(f"   • Plant: {result.power_plant_name}")
        print(f"   • Confidence: {result.confidence_score:.2f}")
        print(f"   • Level: {result.level.value}")
        print(f"   • Tier used: {result.search_metadata.tier_used.value}")
        print(f"   • Sources found: {result.search_metadata.sources_found}")
        print(f"   • Agencies: {len(result.credit_rating)}")
        print(f"   • Duration: {result.search_metadata.search_duration_seconds:.1f}s")
        
        # Save test result
        output_dir = Path("./test_results")
        output_dir.mkdir(exist_ok=True)
        
        output_file = output_dir / f"{test_plant.replace(' ', '_')}_test.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result.dict(), f, indent=2, default=str, ensure_ascii=False)
        
        print(f"   • Result saved to: {output_file}")
        
        await agent.cleanup()
        return True
        
    except Exception as e:
        print(f"❌ Single search test failed: {str(e)}")
        return False


async def test_schema_validation():
    """Test data schema validation."""
    print("\n📋 Testing schema validation...")
    
    try:
        from credit_rating_schemas import (
            CreditRatingResult, AgencyRating, YearwiseRating, 
            RatingAgency, Currency, SourceLevel, SearchTier, SearchMetadata
        )
        
        # Create a test result
        test_result = CreditRatingResult(
            power_plant_name="Test Plant",
            credit_rating=[
                AgencyRating(
                    agency=RatingAgency.CRISIL,
                    name="CRISIL Limited",
                    yearwise_rating=[
                        YearwiseRating(
                            rating="AA- Stable",
                            rating_trunc="AA-",
                            year="2023",
                            source_url="https://example.com"
                        )
                    ]
                )
            ],
            credit_rating_note="Test rating note",
            currency=Currency.INR,
            level=SourceLevel.POWER_PLANT,
            confidence_score=0.85,
            search_metadata=SearchMetadata(
                tier_used=SearchTier.DIRECT_PLANT,
                queries_executed=["test query"],
                sources_found=1,
                search_duration_seconds=10.0
            )
        )
        
        # Validate by converting to dict and back
        result_dict = test_result.dict()
        reconstructed = CreditRatingResult(**result_dict)
        
        print("✅ Schema validation passed")
        print(f"   • All required fields present")
        print(f"   • Data types validated")
        print(f"   • Serialization/deserialization works")
        
        return True
        
    except Exception as e:
        print(f"❌ Schema validation failed: {str(e)}")
        return False


async def test_utility_functions():
    """Test utility functions."""
    print("\n🛠️  Testing utility functions...")
    
    try:
        from utils import (
            sanitize_filename, extract_rating_from_text, 
            clean_rating_string, calculate_confidence_score
        )
        
        # Test filename sanitization
        test_filename = "Adani Mundra Power Plant (Gujarat) - 2024"
        sanitized = sanitize_filename(test_filename)
        print(f"   • Filename sanitization: '{test_filename}' -> '{sanitized}'")
        
        # Test rating extraction
        test_text = "The company has a CRISIL rating of AA- Stable for 2023"
        rating = extract_rating_from_text(test_text)
        print(f"   • Rating extraction: Found '{rating}' in text")
        
        # Test rating cleaning
        dirty_rating = "AA- Stable Watch"
        clean_rating = clean_rating_string(dirty_rating)
        print(f"   • Rating cleaning: '{dirty_rating}' -> '{clean_rating}'")
        
        # Test confidence calculation
        confidence = calculate_confidence_score(2, 3, 1, True)
        print(f"   • Confidence calculation: {confidence:.2f}")
        
        print("✅ Utility functions test passed")
        return True
        
    except Exception as e:
        print(f"❌ Utility functions test failed: {str(e)}")
        return False


async def run_all_tests():
    """Run all tests."""
    print("🧪 Credit Rating Research Agent - Test Suite")
    print("=" * 60)
    
    # Setup logging for tests
    setup_logging(verbose=True)
    
    tests = [
        ("Environment Setup", test_environment),
        ("Schema Validation", test_schema_validation),
        ("Utility Functions", test_utility_functions),
        ("Agent Initialization", test_agent_initialization),
        ("Single Search", test_single_search),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            if result:
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {str(e)}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The agent is ready to use.")
        print("\nNext steps:")
        print("1. Set up your .env file with GEMINI_API_KEY")
        print("2. Run: python main.py 'Your Power Plant Name'")
        print("3. Or try interactive mode: python main.py --interactive")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        return False
    
    return True


if __name__ == "__main__":
    asyncio.run(run_all_tests())
