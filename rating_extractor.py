"""
Credit Rating Extractor

This module extracts and processes credit rating information from search results.
It handles rating standardization, agency identification, and temporal organization.
"""

import logging
import re
from typing import List, Dict, Any, Optional

try:
    from langchain_google_genai import ChatGoogleGenerativeAI
    from langchain_core.messages import HumanMessage
    from pydantic import BaseModel
except ImportError as e:
    print(f"Warning: Some dependencies not available: {e}")
    # Create mock classes for development
    class BaseModel:
        pass
    class ChatGoogleGenerativeAI:
        pass
    class HumanMessage:
        pass

from credit_rating_schemas import (
    AgencyRating, YearwiseRating, RatingAgency
)
from utils import clean_rating_string, extract_year_from_text


class RatingExtractionResult(BaseModel):
    """Result from rating extraction process."""
    agency: str
    rating: str
    year: str
    source_url: str
    confidence: float


class CreditRatingExtractor:
    """Extracts credit ratings from search results using LLM and pattern matching."""
    
    def __init__(self, llm: ChatGoogleGenerativeAI):
        """
        Initialize the rating extractor.
        
        Args:
            llm: Language model for structured extraction
        """
        self.llm = llm
        self.logger = logging.getLogger(__name__)
        
        # Rating agency patterns for identification
        self.agency_patterns = {
            RatingAgency.CRISIL: [r'\bCRISIL\b', r'\bCRI\b'],
            RatingAgency.ICRA: [r'\bICRA\b', r'\bICR\b'],
            RatingAgency.CARE: [r'\bCARE\b', r'\bCAR\b'],
            RatingAgency.INDIA_RATINGS: [r'\bIndia Ratings\b', r'\bIND\b', r'\bFitch India\b'],
            RatingAgency.BRICKWORK: [r'\bBrickwork\b', r'\bBWR\b'],
            RatingAgency.MOODYS: [r'\bMoody\'?s\b', r'\bMDY\b'],
            RatingAgency.SP_GLOBAL: [r'\bS&P\b', r'\bStandard & Poor\'?s\b', r'\bSPG\b'],
            RatingAgency.FITCH: [r'\bFitch\b', r'\bFTC\b'],
            RatingAgency.SCOPE: [r'\bScope\b', r'\bSCP\b']
        }
        
        # Rating scale patterns
        self.rating_patterns = {
            'moodys': r'\b(Aaa|Aa[1-3]|A[1-3]|Baa[1-3]|Ba[1-3]|B[1-3]|Caa[1-3]|Ca|C)\b',
            'sp_fitch': r'\b(AAA|AA[+-]?|A[+-]?|BBB[+-]?|BB[+-]?|B[+-]?|CCC[+-]?|CC|C|D)\b',
            'indian': r'\b(LAAA|LAA[+-]?|LA[+-]?|LBBB[+-]?|LBB[+-]?|LB[+-]?|LCCC[+-]?|LCD|LD)\b'
        }
    
    async def extract_ratings_from_results(
        self, 
        search_results: List[Dict[str, Any]], 
        entity_name: str,
        target_years: List[str]
    ) -> List[AgencyRating]:
        """
        Extract credit ratings from search results.
        
        Args:
            search_results: List of search result dictionaries
            entity_name: Name of the entity being rated
            target_years: Years to look for ratings
            
        Returns:
            List of agency ratings
        """
        self.logger.info(f"Extracting ratings from {len(search_results)} search results")
        
        # Collect all potential rating extractions
        raw_extractions = []
        
        for result in search_results:
            extractions = await self._extract_from_single_result(result, target_years)
            raw_extractions.extend(extractions)
        
        # Group by agency and organize
        agency_ratings = self._organize_by_agency(raw_extractions)
        
        self.logger.info(f"Extracted ratings from {len(agency_ratings)} agencies")
        return agency_ratings
    
    async def _extract_from_single_result(
        self, 
        search_result: Dict[str, Any], 
        target_years: List[str]
    ) -> List[RatingExtractionResult]:
        """Extract ratings from a single search result."""
        extractions = []
        
        # Get response text
        response_text = str(search_result.get("response", ""))
        
        # Get source URLs
        sources = search_result.get("sources", [])
        source_url = sources[0]["url"] if sources else "Unknown"
        
        # Try pattern-based extraction first
        pattern_extractions = self._extract_with_patterns(response_text, source_url, target_years)
        extractions.extend(pattern_extractions)
        
        # Try LLM-based extraction for more complex cases
        if len(pattern_extractions) < 2:  # If we didn't find much with patterns
            llm_extractions = await self._extract_with_llm(response_text, source_url, target_years)
            extractions.extend(llm_extractions)
        
        return extractions
    
    def _extract_with_patterns(
        self, 
        text: str, 
        source_url: str, 
        target_years: List[str]
    ) -> List[RatingExtractionResult]:
        """Extract ratings using regex patterns."""
        extractions = []
        
        # Split text into sentences for better context
        sentences = re.split(r'[.!?]+', text)
        
        for sentence in sentences:
            # Look for rating patterns
            for scale_type, pattern in self.rating_patterns.items():
                rating_matches = re.findall(pattern, sentence, re.IGNORECASE)
                
                if rating_matches:
                    # Try to identify agency in the same sentence
                    agency = self._identify_agency(sentence)
                    
                    # Try to extract year
                    year = extract_year_from_text(sentence)
                    if not year:
                        # Default to most recent target year
                        year = max(target_years) if target_years else "2024"
                    
                    for rating in rating_matches:
                        if agency:
                            extractions.append(RatingExtractionResult(
                                agency=agency.value,
                                rating=rating,
                                year=year,
                                source_url=source_url,
                                confidence=0.8
                            ))
        
        return extractions
    
    async def _extract_with_llm(
        self, 
        text: str, 
        source_url: str, 
        target_years: List[str]
    ) -> List[RatingExtractionResult]:
        """Extract ratings using LLM for complex cases."""
        try:
            prompt = f"""
            Extract credit rating information from the following text. Look for:
            1. Rating agency names (CRISIL, ICRA, CARE, Moody's, S&P, Fitch, etc.)
            2. Credit ratings (AAA, AA+, AA, AA-, A+, A, A-, BBB+, etc.)
            3. Years when ratings were assigned
            
            Target years: {', '.join(target_years)}
            
            Text to analyze:
            {text[:2000]}  # Limit text length
            
            Return findings in this format:
            Agency: [agency name]
            Rating: [rating]
            Year: [year]
            ---
            
            If no ratings found, return "No ratings found"
            """
            
            response = await self.llm.ainvoke([HumanMessage(content=prompt)])
            response_text = response.content
            
            # Parse LLM response
            extractions = self._parse_llm_response(response_text, source_url)
            return extractions
            
        except Exception as e:
            self.logger.warning(f"LLM extraction failed: {str(e)}")
            return []
    
    def _parse_llm_response(self, response_text: str, source_url: str) -> List[RatingExtractionResult]:
        """Parse LLM response to extract rating information."""
        extractions = []
        
        if "No ratings found" in response_text:
            return extractions
        
        # Split by --- separator
        sections = response_text.split('---')
        
        for section in sections:
            lines = section.strip().split('\n')
            agency = None
            rating = None
            year = None
            
            for line in lines:
                line = line.strip()
                if line.startswith('Agency:'):
                    agency = line.replace('Agency:', '').strip()
                elif line.startswith('Rating:'):
                    rating = line.replace('Rating:', '').strip()
                elif line.startswith('Year:'):
                    year = line.replace('Year:', '').strip()
            
            if agency and rating:
                # Try to map agency name to enum
                agency_enum = self._map_agency_name(agency)
                if agency_enum:
                    extractions.append(RatingExtractionResult(
                        agency=agency_enum.value,
                        rating=rating,
                        year=year or "2024",
                        source_url=source_url,
                        confidence=0.6
                    ))
        
        return extractions
    
    def _identify_agency(self, text: str) -> Optional[RatingAgency]:
        """Identify rating agency from text."""
        text_lower = text.lower()
        
        for agency, patterns in self.agency_patterns.items():
            for pattern in patterns:
                if re.search(pattern, text, re.IGNORECASE):
                    return agency
        
        return None
    
    def _map_agency_name(self, agency_name: str) -> Optional[RatingAgency]:
        """Map agency name string to RatingAgency enum."""
        agency_name_lower = agency_name.lower()
        
        mapping = {
            'crisil': RatingAgency.CRISIL,
            'icra': RatingAgency.ICRA,
            'care': RatingAgency.CARE,
            'india ratings': RatingAgency.INDIA_RATINGS,
            'brickwork': RatingAgency.BRICKWORK,
            'moody': RatingAgency.MOODYS,
            'moodys': RatingAgency.MOODYS,
            's&p': RatingAgency.SP_GLOBAL,
            'standard & poor': RatingAgency.SP_GLOBAL,
            'fitch': RatingAgency.FITCH,
            'scope': RatingAgency.SCOPE
        }
        
        for key, agency in mapping.items():
            if key in agency_name_lower:
                return agency
        
        return None
    
    def _organize_by_agency(self, extractions: List[RatingExtractionResult]) -> List[AgencyRating]:
        """Organize extractions by agency."""
        agency_dict = {}
        
        for extraction in extractions:
            agency_key = extraction.agency
            
            if agency_key not in agency_dict:
                # Get agency enum and full name
                agency_enum = None
                agency_name = extraction.agency
                
                for enum_val in RatingAgency:
                    if enum_val.value == agency_key:
                        agency_enum = enum_val
                        break
                
                if agency_enum == RatingAgency.CRISIL:
                    agency_name = "CRISIL Limited"
                elif agency_enum == RatingAgency.ICRA:
                    agency_name = "ICRA Limited"
                elif agency_enum == RatingAgency.CARE:
                    agency_name = "CARE Ratings Limited"
                elif agency_enum == RatingAgency.MOODYS:
                    agency_name = "Moody's Investors Service"
                elif agency_enum == RatingAgency.SP_GLOBAL:
                    agency_name = "S&P Global Ratings"
                elif agency_enum == RatingAgency.FITCH:
                    agency_name = "Fitch Ratings"
                # Add more mappings as needed
                
                agency_dict[agency_key] = AgencyRating(
                    agency=agency_enum or RatingAgency.CRISIL,  # Default fallback
                    name=agency_name,
                    yearwise_rating=[]
                )
            
            # Add yearwise rating
            clean_rating = clean_rating_string(extraction.rating)
            
            yearwise_rating = YearwiseRating(
                rating=extraction.rating,
                rating_trunc=clean_rating,
                year=extraction.year,
                source_url=extraction.source_url
            )
            
            agency_dict[agency_key].yearwise_rating.append(yearwise_rating)
        
        # Remove duplicates and sort by year
        for agency_rating in agency_dict.values():
            # Remove duplicate ratings for the same year
            seen = set()
            unique_ratings = []
            
            for rating in agency_rating.yearwise_rating:
                key = (rating.year, rating.rating_trunc)
                if key not in seen:
                    seen.add(key)
                    unique_ratings.append(rating)
            
            # Sort by year (most recent first)
            agency_rating.yearwise_rating = sorted(
                unique_ratings, 
                key=lambda x: int(x.year), 
                reverse=True
            )
        
        return list(agency_dict.values())
