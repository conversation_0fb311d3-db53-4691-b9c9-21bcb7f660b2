"""
Credit Rating Agent Data Schemas

This module defines the data structures and schemas for the credit rating research agent.
Includes models for credit ratings, search metadata, and output formatting.
"""

from datetime import datetime
from enum import Enum
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, validator


class RatingAgency(str, Enum):
    """Enumeration of supported credit rating agencies."""
    # Indian Agencies
    CRISIL = "CRISIL"
    ICRA = "ICRA" 
    CARE = "CARE"
    INDIA_RATINGS = "IND"
    BRICKWORK = "BWR"
    
    # International Agencies
    MOODYS = "MDY"
    SP_GLOBAL = "SPG"
    FITCH = "FTC"
    SCOPE = "SCP"


class Currency(str, Enum):
    """Supported currencies for credit ratings."""
    INR = "INR"
    USD = "USD"
    EUR = "EUR"
    GBP = "GBP"


class SourceLevel(str, Enum):
    """Source level for credit rating information."""
    POWER_PLANT = "power_plant"
    PARENT_COMPANY = "parent_company"


class SearchTier(int, Enum):
    """Search tier levels for the multi-tier search strategy."""
    DIRECT_PLANT = 1
    CORPORATE_DISCOVERY = 2
    PARENT_COMPANY = 3


class YearwiseRating(BaseModel):
    """Individual rating for a specific year."""
    rating: str = Field(..., description="Complete rating string with outlook (e.g., 'AA- Stable')")
    rating_trunc: str = Field(..., description="Core rating symbol without modifiers (e.g., 'AA-')")
    year: str = Field(..., description="Year of the rating")
    source_url: str = Field(..., description="Source URL for verification")
    instrument_type: Optional[str] = Field(None, description="Type of instrument rated (e.g., 'long-term debt', 'corporate rating')")
    
    @validator('rating_trunc', pre=False, always=True)
    def validate_rating_trunc(cls, v, values):
        """Remove outlook modifiers from rating."""
        if not v and 'rating' in values:
            # Remove common outlook modifiers
            outlook_modifiers = ['Stable', 'Positive', 'Negative', 'Watch', 'Under Review', 'CreditWatch']
            rating_clean = values['rating']
            for modifier in outlook_modifiers:
                rating_clean = rating_clean.replace(modifier, '').strip()
            return rating_clean
        return v


class AgencyRating(BaseModel):
    """Credit rating information from a specific agency."""
    agency: RatingAgency = Field(..., description="Rating agency code")
    name: str = Field(..., description="Full name of the rating agency")
    yearwise_rating: List[YearwiseRating] = Field(default_factory=list, description="List of ratings by year")


class SearchMetadata(BaseModel):
    """Metadata about the search process."""
    tier_used: SearchTier = Field(..., description="Highest tier reached in search")
    queries_executed: List[str] = Field(default_factory=list, description="List of search queries executed")
    sources_found: int = Field(0, description="Number of sources found")
    search_duration_seconds: float = Field(0.0, description="Total search duration in seconds")
    parent_company_identified: Optional[str] = Field(None, description="Parent company name if identified")


class CreditRatingResult(BaseModel):
    """Complete credit rating research result for a power plant."""
    power_plant_name: str = Field(..., description="Input power plant name")
    search_timestamp: datetime = Field(default_factory=datetime.now, description="When the search was conducted")
    credit_rating: List[AgencyRating] = Field(default_factory=list, description="Credit ratings from various agencies")
    credit_rating_note: str = Field(..., description="Detailed explanation of rating source and methodology")
    currency: Currency = Field(Currency.INR, description="Primary currency for the ratings")
    level: SourceLevel = Field(..., description="Source level of the rating information")
    confidence_score: float = Field(0.0, ge=0.0, le=1.0, description="Confidence score for the results")
    search_metadata: SearchMetadata = Field(..., description="Metadata about the search process")


class SearchQuery(BaseModel):
    """Individual search query with context."""
    query: str = Field(..., description="The search query string")
    tier: SearchTier = Field(..., description="Search tier this query belongs to")
    rationale: str = Field(..., description="Explanation for this query")


class SearchQueryList(BaseModel):
    """List of search queries for a specific tier."""
    queries: List[SearchQuery] = Field(..., description="List of search queries")
    tier: SearchTier = Field(..., description="Search tier for these queries")
    target_entity: str = Field(..., description="Entity being searched (plant name or company name)")


class CorporateStructure(BaseModel):
    """Information about corporate ownership structure."""
    power_plant_name: str = Field(..., description="Power plant name")
    parent_company: Optional[str] = Field(None, description="Parent company name")
    holding_company: Optional[str] = Field(None, description="Holding company name")
    operator: Optional[str] = Field(None, description="Operating company name")
    ownership_percentage: Optional[float] = Field(None, description="Ownership percentage if available")
    source_url: str = Field(..., description="Source URL for ownership information")


class RegionalAgencyMapping(BaseModel):
    """Mapping of regions to preferred rating agencies."""
    region: str = Field(..., description="Region or country name")
    primary_agencies: List[RatingAgency] = Field(..., description="Primary rating agencies for this region")
    secondary_agencies: List[RatingAgency] = Field(default_factory=list, description="Secondary agencies")


# Default regional mappings
DEFAULT_REGIONAL_MAPPINGS = [
    RegionalAgencyMapping(
        region="India",
        primary_agencies=[RatingAgency.CRISIL, RatingAgency.ICRA, RatingAgency.CARE],
        secondary_agencies=[RatingAgency.INDIA_RATINGS, RatingAgency.BRICKWORK]
    ),
    RegionalAgencyMapping(
        region="United States",
        primary_agencies=[RatingAgency.MOODYS, RatingAgency.SP_GLOBAL, RatingAgency.FITCH],
        secondary_agencies=[]
    ),
    RegionalAgencyMapping(
        region="Europe",
        primary_agencies=[RatingAgency.FITCH, RatingAgency.SP_GLOBAL, RatingAgency.MOODYS],
        secondary_agencies=[RatingAgency.SCOPE]
    ),
    RegionalAgencyMapping(
        region="United Kingdom",
        primary_agencies=[RatingAgency.FITCH, RatingAgency.SP_GLOBAL, RatingAgency.MOODYS],
        secondary_agencies=[RatingAgency.SCOPE]
    )
]


class CreditRatingConfig(BaseModel):
    """Configuration for the credit rating agent."""
    target_years: List[str] = Field(default=["2019", "2020", "2021", "2022", "2023", "2024"], description="Years to search for ratings")
    max_search_duration: int = Field(default=30, description="Maximum search duration per tier in seconds")
    max_retries: int = Field(default=3, description="Maximum number of retries for failed searches")
    regional_mappings: List[RegionalAgencyMapping] = Field(default_factory=lambda: DEFAULT_REGIONAL_MAPPINGS)
    output_directory: str = Field(default="./results", description="Directory to save results")
    enable_caching: bool = Field(default=True, description="Enable search result caching")
    confidence_threshold: float = Field(default=0.5, description="Minimum confidence score for results")


class SearchError(BaseModel):
    """Error information for failed searches."""
    tier: SearchTier = Field(..., description="Search tier where error occurred")
    query: str = Field(..., description="Query that failed")
    error_message: str = Field(..., description="Error message")
    timestamp: datetime = Field(default_factory=datetime.now, description="When the error occurred")


class SearchResult(BaseModel):
    """Result from a single search operation."""
    query: str = Field(..., description="Search query executed")
    tier: SearchTier = Field(..., description="Search tier")
    success: bool = Field(..., description="Whether the search was successful")
    sources_found: int = Field(0, description="Number of sources found")
    ratings_extracted: int = Field(0, description="Number of ratings extracted")
    duration_seconds: float = Field(0.0, description="Search duration")
    error: Optional[SearchError] = Field(None, description="Error information if search failed")
    search_results: List[Dict[str, Any]] = Field(default_factory=list, description="Raw search results")
    queries_executed: List[str] = Field(default_factory=list, description="List of executed queries")
    ratings_found: int = Field(0, description="Number of ratings found")
