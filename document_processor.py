"""
Document Processing Module for Credit Rating Agent

This module handles processing of various document types including PDFs
to extract credit rating information.
"""

import logging
import re
import requests
from typing import List, Dict, Any, Optional, Tuple
from urllib.parse import urlparse
import tempfile
import os

try:
    import PyPDF2
    import pdfplumber
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False
    print("Warning: PDF processing libraries not available. Install with: pip install PyPDF2 pdfplumber")


class DocumentProcessor:
    """Processes documents to extract credit rating information."""
    
    def __init__(self):
        """Initialize the document processor."""
        self.logger = logging.getLogger(__name__)
        
        # Rating patterns for different agencies
        self.rating_patterns = {
            'indian_agencies': [
                r'\b(LAAA|LAA[+-]?|LA[+-]?|LBBB[+-]?|LBB[+-]?|LB[+-]?|LCCC[+-]?|LCD|LD)\b',
                r'\b(AAA|AA[+-]?|A[+-]?|BBB[+-]?|BB[+-]?|B[+-]?|CCC[+-]?|CC|C|D)\b',
                r'\bCRISIL\s+(AAA|AA[+-]?|A[+-]?|BBB[+-]?|BB[+-]?|B[+-]?|C|D)\b',
                r'\bICRA\s+(AAA|AA[+-]?|A[+-]?|BBB[+-]?|BB[+-]?|B[+-]?|C|D)\b',
                r'\bCARE\s+(AAA|AA[+-]?|A[+-]?|BBB[+-]?|BB[+-]?|B[+-]?|C|D)\b',
            ],
            'international_agencies': [
                r'\b(Aaa|Aa[1-3]|A[1-3]|Baa[1-3]|Ba[1-3]|B[1-3]|Caa[1-3]|Ca|C)\b',  # Moody's
                r'\b(AAA|AA[+-]?|A[+-]?|BBB[+-]?|BB[+-]?|B[+-]?|CCC[+-]?|CC|C|D)\b',  # S&P/Fitch
            ]
        }
    
    def can_process_pdfs(self) -> bool:
        """Check if PDF processing is available."""
        return PDF_AVAILABLE
    
    async def process_document_urls(self, urls: List[str]) -> List[Dict[str, Any]]:
        """
        Process a list of document URLs and extract credit rating information.
        
        Args:
            urls: List of document URLs to process
            
        Returns:
            List of extracted information from documents
        """
        results = []
        
        for url in urls:
            try:
                result = await self._process_single_url(url)
                if result:
                    results.append(result)
            except Exception as e:
                self.logger.warning(f"Failed to process URL {url}: {str(e)}")
                continue
        
        return results
    
    async def _process_single_url(self, url: str) -> Optional[Dict[str, Any]]:
        """Process a single document URL."""
        # Check if it's a PDF URL
        if self._is_pdf_url(url):
            if not self.can_process_pdfs():
                self.logger.warning(f"PDF processing not available for {url}")
                return None
            return await self._process_pdf_url(url)
        else:
            # For non-PDF URLs, we could add HTML processing here
            self.logger.debug(f"Skipping non-PDF URL: {url}")
            return None
    
    def _is_pdf_url(self, url: str) -> bool:
        """Check if URL points to a PDF document."""
        parsed = urlparse(url)
        path = parsed.path.lower()
        
        # Check file extension
        if path.endswith('.pdf'):
            return True
        
        # Check for PDF indicators in URL
        pdf_indicators = ['pdf', 'document', 'report', 'rating']
        return any(indicator in url.lower() for indicator in pdf_indicators)
    
    async def _process_pdf_url(self, url: str) -> Optional[Dict[str, Any]]:
        """Download and process a PDF from URL."""
        try:
            # Download PDF
            response = requests.get(url, timeout=30, headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })
            response.raise_for_status()
            
            # Save to temporary file
            with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
                temp_file.write(response.content)
                temp_path = temp_file.name
            
            try:
                # Process the PDF
                result = self._extract_from_pdf(temp_path)
                if result:
                    result['source_url'] = url
                return result
            finally:
                # Clean up temporary file
                os.unlink(temp_path)
                
        except Exception as e:
            self.logger.error(f"Error processing PDF from {url}: {str(e)}")
            return None
    
    def _extract_from_pdf(self, pdf_path: str) -> Optional[Dict[str, Any]]:
        """Extract credit rating information from a PDF file."""
        extracted_data = {
            'ratings_found': [],
            'text_content': '',
            'agencies_mentioned': [],
            'years_found': []
        }
        
        try:
            # Try pdfplumber first (better for tables and structured data)
            with pdfplumber.open(pdf_path) as pdf:
                full_text = ""
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        full_text += page_text + "\n"
                
                if full_text.strip():
                    extracted_data['text_content'] = full_text
                    self._extract_ratings_from_text(full_text, extracted_data)
                    return extracted_data
        
        except Exception as e:
            self.logger.warning(f"pdfplumber failed: {str(e)}, trying PyPDF2")
        
        try:
            # Fallback to PyPDF2
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                full_text = ""
                
                for page in pdf_reader.pages:
                    page_text = page.extract_text()
                    if page_text:
                        full_text += page_text + "\n"
                
                if full_text.strip():
                    extracted_data['text_content'] = full_text
                    self._extract_ratings_from_text(full_text, extracted_data)
                    return extracted_data
        
        except Exception as e:
            self.logger.error(f"PyPDF2 also failed: {str(e)}")
        
        return None
    
    def _extract_ratings_from_text(self, text: str, extracted_data: Dict[str, Any]) -> None:
        """Extract credit ratings from text content."""
        # Find rating agencies
        agencies = self._find_agencies(text)
        extracted_data['agencies_mentioned'] = agencies
        
        # Find years
        years = self._find_years(text)
        extracted_data['years_found'] = years
        
        # Find ratings
        ratings = self._find_ratings(text)
        extracted_data['ratings_found'] = ratings
    
    def _find_agencies(self, text: str) -> List[str]:
        """Find rating agencies mentioned in text."""
        agencies = []
        agency_patterns = {
            'CRISIL': r'\bCRISIL\b',
            'ICRA': r'\bICRA\b',
            'CARE': r'\bCARE\b',
            'India Ratings': r'\bIndia\s+Ratings\b',
            'Brickwork': r'\bBrickwork\b',
            'Moody\'s': r'\bMoody\'?s\b',
            'S&P': r'\bS&P\b|\bStandard\s+&\s+Poor\'?s\b',
            'Fitch': r'\bFitch\b'
        }
        
        for agency, pattern in agency_patterns.items():
            if re.search(pattern, text, re.IGNORECASE):
                agencies.append(agency)
        
        return agencies
    
    def _find_years(self, text: str) -> List[str]:
        """Find years mentioned in text (2019-2024)."""
        year_pattern = r'\b(20(?:19|2[0-4]))\b'
        years = re.findall(year_pattern, text)
        return list(set(years))  # Remove duplicates
    
    def _find_ratings(self, text: str) -> List[Dict[str, Any]]:
        """Find credit ratings in text."""
        ratings = []
        
        # Search for all rating patterns
        for category, patterns in self.rating_patterns.items():
            for pattern in patterns:
                matches = re.finditer(pattern, text, re.IGNORECASE)
                for match in matches:
                    rating = match.group(0)
                    # Get context around the rating
                    start = max(0, match.start() - 100)
                    end = min(len(text), match.end() + 100)
                    context = text[start:end]
                    
                    ratings.append({
                        'rating': rating,
                        'context': context,
                        'position': match.start(),
                        'category': category
                    })
        
        return ratings
    
    def enhance_search_with_documents(self, search_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Enhance search results by processing any document URLs found.
        
        Args:
            search_results: Original search results
            
        Returns:
            Enhanced search results with document processing
        """
        enhanced_results = []
        
        for result in search_results:
            enhanced_result = result.copy()
            
            # Look for PDF URLs in the sources
            sources = result.get('sources', [])
            pdf_urls = []
            
            for source in sources:
                url = source.get('url', '')
                if self._is_pdf_url(url):
                    pdf_urls.append(url)
            
            # Process PDF documents if found
            if pdf_urls and self.can_process_pdfs():
                try:
                    import asyncio
                    loop = asyncio.get_event_loop()
                    document_data = loop.run_until_complete(
                        self.process_document_urls(pdf_urls)
                    )
                    
                    if document_data:
                        enhanced_result['document_data'] = document_data
                        # Add document text to the response for rating extraction
                        doc_text = ' '.join([doc.get('text_content', '') for doc in document_data])
                        if doc_text:
                            enhanced_result['response'] = str(enhanced_result.get('response', '')) + ' ' + doc_text
                
                except Exception as e:
                    self.logger.warning(f"Failed to process documents: {str(e)}")
            
            enhanced_results.append(enhanced_result)
        
        return enhanced_results
