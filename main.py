#!/usr/bin/env python3
"""
Credit Rating Research Agent - Main Application

A standalone Python application for automatically finding and extracting 
credit ratings for power plants worldwide.

Usage:
    python main.py "Plant Name"
    python main.py --interactive
    python main.py --batch plants_list.txt
"""

import argparse
import asyncio
import json
import logging
import os
import sys
import time
from pathlib import Path
from typing import List, Optional

from credit_rating_agent import CreditRatingAgent
from credit_rating_schemas import CreditRatingConfig, CreditRatingResult
from utils import setup_logging, sanitize_filename, load_plant_list


def setup_argument_parser() -> argparse.ArgumentParser:
    """Set up command line argument parser."""
    parser = argparse.ArgumentParser(
        description="Credit Rating Research Agent for Power Plants",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python main.py "Adani Mundra Power Plant"
    python main.py --interactive
    python main.py --batch plants_list.txt --output-dir ./custom_results/
    python main.py "Plant Name" --verbose --max-duration 60
        """
    )
    
    # Mutually exclusive group for input methods
    input_group = parser.add_mutually_exclusive_group(required=True)
    input_group.add_argument(
        "plant_name",
        nargs="?",
        help="Name of the power plant to research"
    )
    input_group.add_argument(
        "--interactive",
        action="store_true",
        help="Run in interactive mode for multiple plants"
    )
    input_group.add_argument(
        "--batch",
        type=str,
        metavar="FILE",
        help="Process multiple plants from a file (one per line)"
    )
    
    # Configuration options
    parser.add_argument(
        "--output-dir",
        type=str,
        default="./results",
        help="Directory to save results (default: ./results)"
    )
    parser.add_argument(
        "--max-duration",
        type=int,
        default=30,
        help="Maximum search duration per tier in seconds (default: 30)"
    )
    parser.add_argument(
        "--confidence-threshold",
        type=float,
        default=0.5,
        help="Minimum confidence score for results (default: 0.5)"
    )
    parser.add_argument(
        "--no-cache",
        action="store_true",
        help="Disable search result caching"
    )
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Enable verbose logging"
    )
    parser.add_argument(
        "--log-file",
        type=str,
        help="Log file path (default: logs to console)"
    )
    
    return parser


async def process_single_plant(
    agent: CreditRatingAgent,
    plant_name: str,
    output_dir: Path,
    verbose: bool = False
) -> Optional[CreditRatingResult]:
    """Process a single power plant and save results."""
    if verbose:
        print(f"\n🔍 Researching credit rating for: {plant_name}")
        print("=" * 60)
    
    start_time = time.time()
    
    try:
        # Execute the search
        result = await agent.research_credit_rating(plant_name)
        
        # Calculate duration
        duration = time.time() - start_time
        
        if verbose:
            print(f"✅ Search completed in {duration:.1f} seconds")
            print(f"📊 Confidence score: {result.confidence_score:.2f}")
            print(f"🏢 Source level: {result.level.value}")
            print(f"💰 Currency: {result.currency.value}")
            print(f"🔍 Tier used: {result.search_metadata.tier_used.value}")
            print(f"📄 Sources found: {result.search_metadata.sources_found}")
            
            if result.credit_rating:
                print(f"🏆 Ratings found from {len(result.credit_rating)} agencies:")
                for agency_rating in result.credit_rating:
                    print(f"   • {agency_rating.name}: {len(agency_rating.yearwise_rating)} ratings")
        
        # Save results to file
        filename = sanitize_filename(f"{plant_name}_credit_rating.json")
        output_path = output_dir / filename
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(result.dict(), f, indent=2, default=str, ensure_ascii=False)
        
        if verbose:
            print(f"💾 Results saved to: {output_path}")
        else:
            print(f"✅ {plant_name} -> {output_path}")
        
        return result
        
    except Exception as e:
        error_msg = f"❌ Error processing {plant_name}: {str(e)}"
        print(error_msg)
        logging.error(error_msg, exc_info=True)
        return None


async def interactive_mode(agent: CreditRatingAgent, output_dir: Path, verbose: bool = False):
    """Run the agent in interactive mode."""
    print("\n🤖 Credit Rating Research Agent - Interactive Mode")
    print("=" * 60)
    print("Enter power plant names one by one. Type 'quit' or 'exit' to stop.")
    print("Type 'help' for usage examples.")
    print()
    
    processed_count = 0
    
    while True:
        try:
            plant_name = input("🏭 Enter power plant name: ").strip()
            
            if not plant_name:
                continue
                
            if plant_name.lower() in ['quit', 'exit', 'q']:
                break
                
            if plant_name.lower() == 'help':
                print("\nUsage examples:")
                print("  • Adani Mundra Power Plant")
                print("  • Tata Power Mundra")
                print("  • NTPC Vindhyachal")
                print("  • Reliance Power Sasan")
                print()
                continue
            
            result = await process_single_plant(agent, plant_name, output_dir, verbose)
            if result:
                processed_count += 1
                
        except KeyboardInterrupt:
            print("\n\n👋 Interrupted by user. Exiting...")
            break
        except EOFError:
            break
    
    print(f"\n📊 Summary: Processed {processed_count} power plants")
    print("👋 Goodbye!")


async def batch_mode(
    agent: CreditRatingAgent,
    batch_file: str,
    output_dir: Path,
    verbose: bool = False
):
    """Process multiple plants from a batch file."""
    try:
        plant_names = load_plant_list(batch_file)
    except Exception as e:
        print(f"❌ Error loading batch file {batch_file}: {e}")
        return
    
    print(f"\n📋 Batch Mode: Processing {len(plant_names)} power plants")
    print("=" * 60)
    
    results = []
    successful = 0
    failed = 0
    
    for i, plant_name in enumerate(plant_names, 1):
        print(f"\n[{i}/{len(plant_names)}] Processing: {plant_name}")
        
        result = await process_single_plant(agent, plant_name, output_dir, verbose)
        if result:
            results.append(result)
            successful += 1
        else:
            failed += 1
    
    # Generate summary report
    summary_path = output_dir / "batch_summary.json"
    summary = {
        "batch_file": batch_file,
        "total_plants": len(plant_names),
        "successful": successful,
        "failed": failed,
        "success_rate": successful / len(plant_names) if plant_names else 0,
        "processed_plants": [r.power_plant_name for r in results],
        "timestamp": time.time()
    }
    
    with open(summary_path, 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, default=str)
    
    print(f"\n📊 Batch Summary:")
    print(f"   • Total plants: {len(plant_names)}")
    print(f"   • Successful: {successful}")
    print(f"   • Failed: {failed}")
    print(f"   • Success rate: {summary['success_rate']:.1%}")
    print(f"   • Summary saved to: {summary_path}")


async def main():
    """Main application entry point."""
    parser = setup_argument_parser()
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(verbose=args.verbose, log_file=args.log_file)
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Create configuration
    config = CreditRatingConfig(
        max_search_duration=args.max_duration,
        confidence_threshold=args.confidence_threshold,
        output_directory=str(output_dir),
        enable_caching=not args.no_cache
    )
    
    # Initialize the agent
    try:
        agent = CreditRatingAgent(config)
        await agent.initialize()
    except Exception as e:
        print(f"❌ Failed to initialize agent: {e}")
        sys.exit(1)
    
    try:
        # Execute based on mode
        if args.interactive:
            await interactive_mode(agent, output_dir, args.verbose)
        elif args.batch:
            await batch_mode(agent, args.batch, output_dir, args.verbose)
        else:
            # Single plant mode
            result = await process_single_plant(agent, args.plant_name, output_dir, args.verbose)
            if not result:
                sys.exit(1)
                
    except KeyboardInterrupt:
        print("\n👋 Interrupted by user. Exiting...")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        logging.error("Unexpected error in main", exc_info=True)
        sys.exit(1)
    finally:
        await agent.cleanup()


if __name__ == "__main__":
    asyncio.run(main())
