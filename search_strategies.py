"""
Search Strategies for Credit Rating Research

This module implements the three-tier search strategy for finding credit ratings:
1. Direct Power Plant Search
2. Corporate Structure Discovery  
3. Parent Company Rating Search
"""

import asyncio
import logging
import time
from typing import List, Dict, Any, Optional, Tuple

try:
    from google.genai import Client
    from langchain_core.messages import HumanMessage
except ImportError as e:
    print(f"Warning: Some dependencies not available: {e}")
    # Create mock classes for development
    class Client:
        pass
    class HumanMessage:
        pass

from credit_rating_schemas import (
    CreditRatingConfig, SearchTier, SearchQuery, SearchResult, 
    CorporateStructure, SearchError
)


class BaseSearchStrategy:
    """Base class for search strategies."""
    
    def __init__(self, genai_client: Client, config: CreditRatingConfig):
        """
        Initialize the search strategy.
        
        Args:
            genai_client: Google GenAI client for search
            config: Configuration for the search
        """
        self.genai_client = genai_client
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def _execute_search_query(self, query: str, max_retries: int = 3) -> Dict[str, Any]:
        """
        Execute a single search query with retry logic.
        
        Args:
            query: Search query to execute
            max_retries: Maximum number of retries
            
        Returns:
            Search results dictionary
        """
        for attempt in range(max_retries):
            try:
                self.logger.debug(f"Executing search query (attempt {attempt + 1}): {query}")
                
                # Use Google Search API through GenAI client
                response = self.genai_client.models.generate_content(
                    model="gemini-2.0-flash",
                    contents=f"Search for: {query}",
                    config={
                        "tools": [{"google_search": {}}],
                        "temperature": 0,
                    },
                )
                
                # Extract search results and grounding metadata
                results = {
                    "query": query,
                    "response": response,
                    "grounding_chunks": [],
                    "sources": []
                }
                
                # Process grounding metadata if available
                if (response.candidates and 
                    hasattr(response.candidates[0], 'grounding_metadata') and
                    response.candidates[0].grounding_metadata):
                    
                    grounding_metadata = response.candidates[0].grounding_metadata
                    if hasattr(grounding_metadata, 'grounding_chunks'):
                        for chunk in grounding_metadata.grounding_chunks:
                            if hasattr(chunk, 'web') and chunk.web:
                                results["grounding_chunks"].append(chunk)
                                results["sources"].append({
                                    "url": chunk.web.uri,
                                    "title": chunk.web.title,
                                    "snippet": getattr(chunk.web, 'snippet', '')
                                })
                
                return results
                
            except Exception as e:
                self.logger.warning(f"Search attempt {attempt + 1} failed: {str(e)}")
                if attempt == max_retries - 1:
                    raise
                await asyncio.sleep(2 ** attempt)  # Exponential backoff
        
        raise RuntimeError(f"Failed to execute search query after {max_retries} attempts")


class DirectPlantSearchStrategy(BaseSearchStrategy):
    """Strategy for direct power plant credit rating search."""
    
    def __init__(self, genai_client: Client, config: CreditRatingConfig):
        super().__init__(genai_client, config)
        self.tier = SearchTier.DIRECT_PLANT
    
    def _generate_search_queries(self, power_plant_name: str) -> List[SearchQuery]:
        """Generate search queries for direct power plant search."""
        base_queries = [
            f'"{power_plant_name}" credit rating',
            f'"{power_plant_name}" financial rating',
            f'"{power_plant_name}" bond rating',
            f'"{power_plant_name}" debt rating',
            f'"{power_plant_name}" CRISIL rating',
            f'"{power_plant_name}" ICRA rating',
            f'"{power_plant_name}" CARE rating',
            f'"{power_plant_name}" Moody\'s rating',
            f'"{power_plant_name}" S&P rating',
            f'"{power_plant_name}" Fitch rating'
        ]
        
        # Add location-specific queries if we can infer location
        location_queries = []
        if "mundra" in power_plant_name.lower():
            location_queries.append(f'"{power_plant_name}" Gujarat credit rating')
        elif "adani" in power_plant_name.lower():
            location_queries.append(f'"{power_plant_name}" Adani credit rating')
        
        all_queries = base_queries + location_queries
        
        return [
            SearchQuery(
                query=query,
                tier=self.tier,
                rationale=f"Direct search for credit rating information of {power_plant_name}"
            )
            for query in all_queries[:8]  # Limit to 8 queries
        ]
    
    async def search(self, power_plant_name: str) -> SearchResult:
        """
        Execute direct power plant search.
        
        Args:
            power_plant_name: Name of the power plant
            
        Returns:
            Search result with findings
        """
        start_time = time.time()
        queries = self._generate_search_queries(power_plant_name)
        
        search_results = []
        successful_queries = []
        ratings_found = 0
        
        self.logger.info(f"Executing {len(queries)} direct plant search queries")
        
        for query in queries:
            try:
                result = await self._execute_search_query(query.query)
                search_results.append(result)
                successful_queries.append(query.query)
                
                # Quick check for rating indicators in response
                response_text = str(result.get("response", "")).lower()
                if any(indicator in response_text for indicator in 
                       ["rating", "rated", "aaa", "aa", "bbb", "crisil", "icra", "care"]):
                    ratings_found += 1
                
            except Exception as e:
                self.logger.warning(f"Query failed: {query.query} - {str(e)}")
                continue
        
        duration = time.time() - start_time
        
        return SearchResult(
            query=f"Direct search for {power_plant_name}",
            tier=self.tier,
            success=len(search_results) > 0,
            sources_found=sum(len(r.get("sources", [])) for r in search_results),
            ratings_extracted=ratings_found,
            duration_seconds=duration,
            search_results=search_results,
            queries_executed=successful_queries
        )


class CorporateDiscoveryStrategy(BaseSearchStrategy):
    """Strategy for discovering corporate structure and ownership."""
    
    def __init__(self, genai_client: Client, config: CreditRatingConfig):
        super().__init__(genai_client, config)
        self.tier = SearchTier.CORPORATE_DISCOVERY
    
    def _generate_ownership_queries(self, power_plant_name: str) -> List[SearchQuery]:
        """Generate queries to discover ownership structure."""
        queries = [
            f'"{power_plant_name}" owner operator',
            f'"{power_plant_name}" parent company',
            f'"{power_plant_name}" holding company',
            f'"{power_plant_name}" subsidiary of',
            f'"{power_plant_name}" owned by',
            f'"{power_plant_name}" developer',
            f'"{power_plant_name}" ownership structure'
        ]
        
        return [
            SearchQuery(
                query=query,
                tier=self.tier,
                rationale=f"Discover corporate ownership structure for {power_plant_name}"
            )
            for query in queries
        ]
    
    async def discover_corporate_structure(self, power_plant_name: str) -> Any:
        """
        Discover corporate structure and ownership.
        
        Args:
            power_plant_name: Name of the power plant
            
        Returns:
            Corporate structure information
        """
        start_time = time.time()
        queries = self._generate_ownership_queries(power_plant_name)
        
        search_results = []
        successful_queries = []
        parent_company = None
        
        self.logger.info(f"Executing {len(queries)} corporate discovery queries")
        
        for query in queries:
            try:
                result = await self._execute_search_query(query.query)
                search_results.append(result)
                successful_queries.append(query.query)
                
                # Extract potential parent company names
                if not parent_company:
                    parent_company = self._extract_parent_company(result, power_plant_name)
                
            except Exception as e:
                self.logger.warning(f"Corporate discovery query failed: {query.query} - {str(e)}")
                continue
        
        duration = time.time() - start_time
        
        # Return a simple result object
        class CorporateDiscoveryResult:
            def __init__(self):
                self.parent_company = parent_company
                self.search_results = search_results
                self.queries_executed = successful_queries
                self.sources_found = sum(len(r.get("sources", [])) for r in search_results)
                self.duration_seconds = duration
        
        return CorporateDiscoveryResult()
    
    def _extract_parent_company(self, search_result: Dict[str, Any], power_plant_name: str) -> Optional[str]:
        """Extract parent company name from search results."""
        response_text = str(search_result.get("response", "")).lower()
        
        # Common patterns for parent companies in India
        company_patterns = [
            "adani", "tata", "reliance", "ntpc", "jsw", "essar", "vedanta",
            "jindal", "gmr", "lanco", "sterlite", "hindalco"
        ]
        
        for pattern in company_patterns:
            if pattern in response_text and pattern not in power_plant_name.lower():
                # Try to extract full company name
                if pattern == "adani":
                    return "Adani Power Limited"
                elif pattern == "tata":
                    return "Tata Power Company Limited"
                elif pattern == "reliance":
                    return "Reliance Power Limited"
                elif pattern == "ntpc":
                    return "NTPC Limited"
                # Add more mappings as needed
        
        return None


class ParentCompanySearchStrategy(BaseSearchStrategy):
    """Strategy for searching parent company credit ratings."""
    
    def __init__(self, genai_client: Client, config: CreditRatingConfig):
        super().__init__(genai_client, config)
        self.tier = SearchTier.PARENT_COMPANY
    
    def _generate_parent_company_queries(self, parent_company: str) -> List[SearchQuery]:
        """Generate search queries for parent company ratings."""
        current_year = "2024"
        queries = []
        
        # Base rating queries
        base_queries = [
            f'"{parent_company}" credit rating {current_year}',
            f'"{parent_company}" credit rating 2023',
            f'"{parent_company}" bond rating',
            f'"{parent_company}" debt rating',
            f'"{parent_company}" financial rating'
        ]
        
        # Agency-specific queries
        agency_queries = [
            f'"{parent_company}" CRISIL rating',
            f'"{parent_company}" ICRA rating',
            f'"{parent_company}" CARE rating',
            f'"{parent_company}" Moody\'s rating',
            f'"{parent_company}" S&P rating'
        ]
        
        all_queries = base_queries + agency_queries
        
        return [
            SearchQuery(
                query=query,
                tier=self.tier,
                rationale=f"Search for credit ratings of parent company {parent_company}"
            )
            for query in all_queries[:10]  # Limit to 10 queries
        ]
    
    async def search(self, parent_company: str, power_plant_name: str) -> SearchResult:
        """
        Execute parent company rating search.
        
        Args:
            parent_company: Name of the parent company
            power_plant_name: Original power plant name for context
            
        Returns:
            Search result with findings
        """
        start_time = time.time()
        queries = self._generate_parent_company_queries(parent_company)
        
        search_results = []
        successful_queries = []
        ratings_found = 0
        
        self.logger.info(f"Executing {len(queries)} parent company search queries for {parent_company}")
        
        for query in queries:
            try:
                result = await self._execute_search_query(query.query)
                search_results.append(result)
                successful_queries.append(query.query)
                
                # Quick check for rating indicators
                response_text = str(result.get("response", "")).lower()
                if any(indicator in response_text for indicator in 
                       ["rating", "rated", "aaa", "aa", "bbb", "crisil", "icra", "care"]):
                    ratings_found += 1
                
            except Exception as e:
                self.logger.warning(f"Parent company query failed: {query.query} - {str(e)}")
                continue
        
        duration = time.time() - start_time
        
        return SearchResult(
            query=f"Parent company search for {parent_company}",
            tier=self.tier,
            success=len(search_results) > 0,
            sources_found=sum(len(r.get("sources", [])) for r in search_results),
            ratings_extracted=ratings_found,
            duration_seconds=duration,
            search_results=search_results,
            queries_executed=successful_queries
        )
