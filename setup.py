#!/usr/bin/env python3
"""
Setup script for Credit Rating Research Agent

This script helps set up the environment and dependencies for the agent.
"""

import os
import subprocess
import sys
from pathlib import Path


def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"   Current version: {sys.version}")
        return False
    
    print(f"✅ Python version: {sys.version.split()[0]}")
    return True


def install_dependencies():
    """Install required dependencies."""
    print("\n📦 Installing dependencies...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False


def setup_environment():
    """Set up environment configuration."""
    print("\n🔧 Setting up environment...")
    
    env_file = Path(".env")
    env_template = Path(".env.template")
    
    if env_file.exists():
        print("✅ .env file already exists")
        return True
    
    if env_template.exists():
        # Copy template to .env
        with open(env_template, 'r') as template:
            content = template.read()
        
        with open(env_file, 'w') as env:
            env.write(content)
        
        print("✅ Created .env file from template")
        print("⚠️  Please edit .env and add your GEMINI_API_KEY")
        return True
    else:
        print("❌ .env.template not found")
        return False


def create_directories():
    """Create necessary directories."""
    print("\n📁 Creating directories...")
    
    directories = ["results", "test_results", "logs"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")
    
    return True


def run_tests():
    """Run basic tests to validate setup."""
    print("\n🧪 Running basic tests...")
    
    try:
        # Import test to check if all modules load correctly
        import credit_rating_schemas
        import utils
        print("✅ All modules import successfully")
        
        # Test schema creation
        from credit_rating_schemas import CreditRatingConfig
        config = CreditRatingConfig()
        print("✅ Schema validation works")
        
        return True
    except Exception as e:
        print(f"❌ Tests failed: {e}")
        return False


def main():
    """Main setup function."""
    print("🚀 Credit Rating Research Agent - Setup")
    print("=" * 50)
    
    steps = [
        ("Python Version Check", check_python_version),
        ("Install Dependencies", install_dependencies),
        ("Setup Environment", setup_environment),
        ("Create Directories", create_directories),
        ("Run Basic Tests", run_tests),
    ]
    
    for step_name, step_func in steps:
        print(f"\n{step_name}...")
        if not step_func():
            print(f"\n❌ Setup failed at: {step_name}")
            return False
    
    print("\n🎉 Setup completed successfully!")
    print("\nNext steps:")
    print("1. Edit .env file and add your GEMINI_API_KEY")
    print("   Get it from: https://aistudio.google.com/app/apikey")
    print("2. Test the installation: python test_agent.py")
    print("3. Run your first search: python main.py 'Adani Mundra Power Plant'")
    print("4. Or try interactive mode: python main.py --interactive")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
