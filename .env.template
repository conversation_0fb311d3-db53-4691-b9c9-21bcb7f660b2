# Credit Rating Research Agent Environment Configuration
# Copy this file to .env and fill in your API keys

# Required: Google Gemini API Key
# Get from: https://aistudio.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here

# Optional: Google Custom Search API (for enhanced search capabilities)
# Get from: https://developers.google.com/custom-search/v1/introduction
GOOGLE_SEARCH_API_KEY=your_google_search_api_key_here
GOOGLE_CSE_ID=your_custom_search_engine_id_here

# Optional: Logging configuration
LOG_LEVEL=INFO
LOG_FILE=credit_rating_agent.log

# Optional: Rate limiting
MAX_REQUESTS_PER_MINUTE=60
SEARCH_DELAY_SECONDS=1
