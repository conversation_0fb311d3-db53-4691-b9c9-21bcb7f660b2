"""
Credit Rating Research Agent

This module implements the core credit rating research agent that performs
multi-tier searches to find credit ratings for power plants worldwide.
"""

import asyncio
import logging
import os
import time
from datetime import datetime
from typing import List, Optional, Dict, Any, Tuple

try:
    from google.genai import Client
    from langchain_google_genai import ChatGoogleGenerativeAI
    from langchain_core.messages import HumanMessage
except ImportError as e:
    print(f"Warning: Some dependencies not available: {e}")
    print("Please install dependencies with: pip install -r requirements.txt")
    # Create mock classes for development
    class Client:
        pass
    class ChatGoogleGenerativeAI:
        pass
    class HumanMessage:
        pass

from credit_rating_schemas import (
    CreditRatingConfig, CreditRatingResult, SearchTier, SourceLevel,
    Currency, RatingAgency, AgencyRating, YearwiseRating, SearchMetadata,
    CorporateStructure, SearchQuery, SearchQueryList, SearchResult
)
from search_strategies import (
    DirectPlantSearchStrategy, CorporateDiscoveryStrategy, ParentCompanySearchStrategy
)
from rating_extractor import CreditRatingExtractor
from utils import calculate_confidence_score, validate_environment


class CreditRatingAgent:
    """
    Main credit rating research agent that orchestrates the multi-tier search process.
    """
    
    def __init__(self, config: CreditRatingConfig):
        """
        Initialize the credit rating agent.
        
        Args:
            config: Configuration for the agent
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Initialize components
        self.genai_client: Optional[Client] = None
        self.llm: Optional[ChatGoogleGenerativeAI] = None
        self.rating_extractor: Optional[CreditRatingExtractor] = None
        
        # Search strategies
        self.direct_plant_strategy: Optional[DirectPlantSearchStrategy] = None
        self.corporate_discovery_strategy: Optional[CorporateDiscoveryStrategy] = None
        self.parent_company_strategy: Optional[ParentCompanySearchStrategy] = None
        
        # Cache for search results
        self.search_cache: Dict[str, Any] = {}
        
    async def initialize(self) -> None:
        """Initialize the agent and its components."""
        self.logger.info("Initializing Credit Rating Agent...")
        
        # Validate environment
        validation = validate_environment()
        if not validation["valid"]:
            raise RuntimeError(f"Environment validation failed: {validation['errors']}")
        
        if validation["warnings"]:
            for warning in validation["warnings"]:
                self.logger.warning(warning)
        
        # Initialize Google GenAI client
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            raise ValueError("GEMINI_API_KEY environment variable is required")
        
        self.genai_client = Client(api_key=api_key)
        
        # Initialize LLM for structured extraction
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-2.0-flash",
            temperature=0.1,
            max_retries=3,
            api_key=api_key
        )
        
        # Initialize rating extractor
        self.rating_extractor = CreditRatingExtractor(self.llm)
        
        # Initialize search strategies
        self.direct_plant_strategy = DirectPlantSearchStrategy(
            self.genai_client, self.config
        )
        self.corporate_discovery_strategy = CorporateDiscoveryStrategy(
            self.genai_client, self.config
        )
        self.parent_company_strategy = ParentCompanySearchStrategy(
            self.genai_client, self.config
        )
        
        self.logger.info("Credit Rating Agent initialized successfully")
    
    async def cleanup(self) -> None:
        """Clean up resources."""
        self.logger.info("Cleaning up Credit Rating Agent...")
        # Add any cleanup logic here if needed
    
    async def research_credit_rating(self, power_plant_name: str) -> CreditRatingResult:
        """
        Research credit rating for a power plant using multi-tier search strategy.
        
        Args:
            power_plant_name: Name of the power plant to research
            
        Returns:
            Complete credit rating research result
        """
        self.logger.info(f"Starting credit rating research for: {power_plant_name}")
        start_time = time.time()
        
        # Initialize result structure
        result = CreditRatingResult(
            power_plant_name=power_plant_name,
            search_timestamp=datetime.now(),
            credit_rating=[],
            credit_rating_note="",
            currency=Currency.INR,  # Default, will be updated based on findings
            level=SourceLevel.POWER_PLANT,  # Default, will be updated
            confidence_score=0.0,
            search_metadata=SearchMetadata(
                tier_used=SearchTier.DIRECT_PLANT,
                queries_executed=[],
                sources_found=0,
                search_duration_seconds=0.0
            )
        )
        
        try:
            # Tier 1: Direct Power Plant Search
            self.logger.info("Executing Tier 1: Direct Power Plant Search")
            tier1_result = await self._execute_tier1_search(power_plant_name)
            
            # Update metadata
            result.search_metadata.queries_executed.extend(tier1_result.queries_executed)
            result.search_metadata.sources_found += tier1_result.sources_found
            
            # If Tier 1 found ratings, process and return
            if tier1_result.ratings_found > 0:
                self.logger.info(f"Tier 1 successful: Found {tier1_result.ratings_found} ratings")
                result.credit_rating = await self._process_search_results(
                    tier1_result.search_results, power_plant_name
                )
                result.level = SourceLevel.POWER_PLANT
                result.search_metadata.tier_used = SearchTier.DIRECT_PLANT
                result.confidence_score = calculate_confidence_score(
                    tier1_result.ratings_found,
                    tier1_result.sources_found,
                    1,
                    has_direct_plant_info=True
                )
                result.credit_rating_note = self._generate_rating_note(
                    SearchTier.DIRECT_PLANT, power_plant_name
                )
            else:
                # Tier 2: Corporate Structure Discovery
                self.logger.info("Tier 1 yielded no results. Executing Tier 2: Corporate Discovery")
                tier2_result = await self._execute_tier2_search(power_plant_name)
                
                result.search_metadata.queries_executed.extend(tier2_result.queries_executed)
                result.search_metadata.sources_found += tier2_result.sources_found
                
                if tier2_result.parent_company:
                    self.logger.info(f"Tier 2 identified parent company: {tier2_result.parent_company}")
                    result.search_metadata.parent_company_identified = tier2_result.parent_company
                    
                    # Tier 3: Parent Company Rating Search
                    self.logger.info("Executing Tier 3: Parent Company Rating Search")
                    tier3_result = await self._execute_tier3_search(
                        tier2_result.parent_company, power_plant_name
                    )
                    
                    result.search_metadata.queries_executed.extend(tier3_result.queries_executed)
                    result.search_metadata.sources_found += tier3_result.sources_found
                    
                    if tier3_result.ratings_found > 0:
                        self.logger.info(f"Tier 3 successful: Found {tier3_result.ratings_found} ratings")
                        result.credit_rating = await self._process_search_results(
                            tier3_result.search_results, tier2_result.parent_company
                        )
                        result.level = SourceLevel.PARENT_COMPANY
                        result.search_metadata.tier_used = SearchTier.PARENT_COMPANY
                        result.confidence_score = calculate_confidence_score(
                            tier3_result.ratings_found,
                            tier3_result.sources_found,
                            3,
                            has_direct_plant_info=False
                        )
                        result.credit_rating_note = self._generate_rating_note(
                            SearchTier.PARENT_COMPANY, power_plant_name, tier2_result.parent_company
                        )
                    else:
                        result.search_metadata.tier_used = SearchTier.PARENT_COMPANY
                        result.credit_rating_note = self._generate_no_results_note(power_plant_name)
                else:
                    result.search_metadata.tier_used = SearchTier.CORPORATE_DISCOVERY
                    result.credit_rating_note = self._generate_no_results_note(power_plant_name)
            
            # Calculate final duration
            result.search_metadata.search_duration_seconds = time.time() - start_time
            
            # Determine currency based on findings
            result.currency = self._determine_currency(result.credit_rating)
            
            self.logger.info(
                f"Research completed for {power_plant_name}. "
                f"Found {len(result.credit_rating)} agencies, "
                f"confidence: {result.confidence_score:.2f}"
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error during research for {power_plant_name}: {str(e)}", exc_info=True)
            result.search_metadata.search_duration_seconds = time.time() - start_time
            result.credit_rating_note = f"Research failed due to error: {str(e)}"
            return result
    
    async def _execute_tier1_search(self, power_plant_name: str) -> SearchResult:
        """Execute Tier 1 direct power plant search."""
        return await self.direct_plant_strategy.search(power_plant_name)
    
    async def _execute_tier2_search(self, power_plant_name: str) -> Any:
        """Execute Tier 2 corporate structure discovery."""
        return await self.corporate_discovery_strategy.discover_corporate_structure(power_plant_name)
    
    async def _execute_tier3_search(self, parent_company: str, power_plant_name: str) -> SearchResult:
        """Execute Tier 3 parent company rating search."""
        return await self.parent_company_strategy.search(parent_company, power_plant_name)
    
    async def _process_search_results(
        self, search_results: List[Dict[str, Any]], entity_name: str
    ) -> List[AgencyRating]:
        """Process search results and extract credit ratings."""
        return await self.rating_extractor.extract_ratings_from_results(
            search_results, entity_name, self.config.target_years
        )
    
    def _determine_currency(self, agency_ratings: List[AgencyRating]) -> Currency:
        """Determine the primary currency for the ratings."""
        # Simple heuristic: default to INR, could be enhanced with region detection
        return Currency.INR
    
    def _generate_rating_note(
        self, tier: SearchTier, power_plant_name: str, parent_company: Optional[str] = None
    ) -> str:
        """Generate explanatory note for the rating source."""
        if tier == SearchTier.DIRECT_PLANT:
            return (
                f"Credit ratings found through direct search for {power_plant_name}. "
                "These ratings are specific to the power plant facility or its direct operating entity."
            )
        elif tier == SearchTier.PARENT_COMPANY:
            return (
                f"Credit ratings found for parent company {parent_company} of {power_plant_name}. "
                "These corporate-level ratings may serve as a proxy for the power plant's creditworthiness, "
                "though facility-specific ratings may differ due to project-specific factors."
            )
        else:
            return f"No specific credit ratings found for {power_plant_name} or its parent entities."
    
    def _generate_no_results_note(self, power_plant_name: str) -> str:
        """Generate note when no results are found."""
        return (
            f"No credit ratings found for {power_plant_name} after comprehensive search across "
            "multiple tiers including direct facility search, corporate structure discovery, "
            "and parent company rating search. The facility may not have public credit ratings "
            "or may be privately held without rated debt instruments."
        )
