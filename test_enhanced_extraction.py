#!/usr/bin/env python3
"""
Test enhanced rating extraction for Haldia Energy Limited
"""

import asyncio
import json
import sys
from pathlib import Path

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from credit_rating_agent import CreditRatingAgent
from credit_rating_schemas import CreditRatingConfig


async def test_enhanced_haldia_extraction():
    """Test enhanced rating extraction specifically for Haldia Energy Limited."""
    print("🔍 Testing Enhanced Rating Extraction for Haldia Energy Limited")
    print("=" * 70)
    
    try:
        config = CreditRatingConfig(
            max_search_duration=60,
            confidence_threshold=0.3,
            output_directory="./test_results"
        )
        
        agent = CreditRatingAgent(config)
        await agent.initialize()
        
        # Test with the exact company name that has ratings
        test_company = "Haldia Energy Limited"
        print(f"🏢 Searching for: {test_company}")
        print("📋 Enhanced extraction features:")
        print("   • CARE format: 'CARE AA-; Negative'")
        print("   • ICRA format: '[ICRA]A1+'")
        print("   • CRISIL format: 'CRISIL AAA'")
        print("   • Standalone ratings with outlook")
        print()
        
        result = await agent.research_credit_rating(test_company)
        
        print("📊 Results:")
        print(f"   • Company: {result.power_plant_name}")
        print(f"   • Confidence: {result.confidence_score:.2f}")
        print(f"   • Level: {result.level.value}")
        print(f"   • Tier used: {result.search_metadata.tier_used.value}")
        print(f"   • Sources found: {result.search_metadata.sources_found}")
        print(f"   • Queries executed: {len(result.search_metadata.queries_executed)}")
        print(f"   • Duration: {result.search_metadata.search_duration_seconds:.1f}s")
        print(f"   • Agencies found: {len(result.credit_rating)}")
        
        if result.credit_rating:
            print("\n🏆 Credit Ratings Found:")
            for agency_rating in result.credit_rating:
                print(f"\n   📊 {agency_rating.name} ({agency_rating.agency.value}):")
                for rating in agency_rating.yearwise_rating:
                    print(f"      • {rating.year}: {rating.rating}")
                    if hasattr(rating, 'rating_trunc') and rating.rating_trunc:
                        print(f"        (Truncated: {rating.rating_trunc})")
        else:
            print("\n❌ No credit ratings found")
        
        print(f"\n📝 Note: {result.credit_rating_note}")
        
        # Save detailed result
        output_dir = Path("./test_results")
        output_dir.mkdir(exist_ok=True)
        
        output_file = output_dir / f"haldia_enhanced_extraction.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result.model_dump(), f, indent=2, default=str, ensure_ascii=False)
        
        print(f"\n💾 Detailed results saved to: {output_file}")
        
        await agent.cleanup()
        
        # Analysis
        print(f"\n📈 Analysis:")
        if len(result.credit_rating) > 0:
            print(f"   ✅ Successfully extracted {len(result.credit_rating)} agency ratings")
            total_ratings = sum(len(agency.yearwise_rating) for agency in result.credit_rating)
            print(f"   📊 Total individual ratings: {total_ratings}")
            
            agencies_found = [agency.agency.value for agency in result.credit_rating]
            print(f"   🏢 Agencies: {', '.join(agencies_found)}")
            
            if 'CARE' in agencies_found:
                print("   ✅ CARE ratings successfully extracted")
            if 'ICRA' in agencies_found:
                print("   ✅ ICRA ratings successfully extracted")
            if 'CRISIL' in agencies_found:
                print("   ✅ CRISIL ratings successfully extracted")
        else:
            print("   ❌ No ratings extracted - extraction patterns may need further refinement")
        
        return len(result.credit_rating) > 0
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def test_direct_rating_extraction():
    """Test rating extraction on known text samples."""
    print("\n🧪 Testing Direct Rating Extraction on Sample Text")
    print("=" * 60)
    
    try:
        from rating_extractor import CreditRatingExtractor
        from google.generativeai import Client
        import os
        
        # Initialize extractor
        client = Client(api_key=os.getenv('GOOGLE_API_KEY'))
        extractor = CreditRatingExtractor(None)  # We'll test pattern-based extraction
        
        # Sample text with known CARE ratings
        sample_text = """
        CARE Ratings has reaffirmed the ratings of Haldia Energy Limited (HEL):
        
        Long-term bank facilities: CARE AA-; Negative (Reaffirmed)
        Short-term bank facilities: CARE A1+ (Reaffirmed)
        Commercial paper (Carved out): CARE A1+ (Reaffirmed)
        
        The ratings were reaffirmed in June 2024.
        """
        
        print("📄 Sample text:")
        print(sample_text.strip())
        print()
        
        # Test pattern extraction
        target_years = ["2024", "2023", "2022", "2021", "2020"]
        extractions = extractor._extract_with_patterns(
            sample_text, 
            "test_source", 
            target_years
        )
        
        print(f"🔍 Extractions found: {len(extractions)}")
        for extraction in extractions:
            print(f"   • {extraction.agency}: {extraction.rating} ({extraction.year})")
        
        return len(extractions) > 0
        
    except Exception as e:
        print(f"❌ Direct extraction test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main test function."""
    print("🧪 Enhanced Rating Extraction Test Suite")
    print("=" * 70)
    
    tests = [
        ("Direct Rating Extraction", test_direct_rating_extraction),
        ("Enhanced Haldia Energy Extraction", test_enhanced_haldia_extraction),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running: {test_name}")
        try:
            result = await test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} passed")
            else:
                print(f"❌ {test_name} failed")
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {str(e)}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed!")
        print("\n💡 Enhanced extraction working correctly:")
        print("   • CARE format patterns working")
        print("   • ICRA format patterns working")
        print("   • Full rating text captured")
        print("   • Year extraction improved")
    else:
        print("\n⚠️  Some tests failed. Check the output above.")
    
    return passed == total


if __name__ == "__main__":
    asyncio.run(main())
