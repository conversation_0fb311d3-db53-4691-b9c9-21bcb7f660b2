"""
Utility functions for the Credit Rating Research Agent.

This module provides helper functions for logging, file operations,
text processing, and other common tasks.
"""

import logging
import re
import unicodedata
from pathlib import Path
from typing import List, Optional, Dict, Any
import json


def setup_logging(verbose: bool = False, log_file: Optional[str] = None) -> None:
    """Set up logging configuration."""
    level = logging.DEBUG if verbose else logging.INFO
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Configure root logger
    logger = logging.getLogger()
    logger.setLevel(level)
    
    # Remove existing handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # File handler if specified
    if log_file:
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)


def sanitize_filename(filename: str, max_length: int = 200) -> str:
    """
    Sanitize a filename by removing or replacing invalid characters.
    
    Args:
        filename: The original filename
        max_length: Maximum length for the filename
        
    Returns:
        A sanitized filename safe for filesystem use
    """
    # Normalize unicode characters
    filename = unicodedata.normalize('NFKD', filename)
    
    # Remove or replace invalid characters
    # Keep alphanumeric, spaces, hyphens, underscores, and dots
    filename = re.sub(r'[^\w\s\-_.]', '', filename)
    
    # Replace multiple spaces with single space
    filename = re.sub(r'\s+', ' ', filename)
    
    # Replace spaces with underscores
    filename = filename.replace(' ', '_')
    
    # Remove leading/trailing dots and spaces
    filename = filename.strip('. ')
    
    # Ensure it's not empty
    if not filename:
        filename = "unnamed_plant"
    
    # Truncate if too long (keeping extension)
    if len(filename) > max_length:
        name_part, ext = filename.rsplit('.', 1) if '.' in filename else (filename, '')
        max_name_length = max_length - len(ext) - 1 if ext else max_length
        filename = name_part[:max_name_length] + ('.' + ext if ext else '')
    
    return filename


def load_plant_list(file_path: str) -> List[str]:
    """
    Load a list of power plant names from a file.
    
    Args:
        file_path: Path to the file containing plant names
        
    Returns:
        List of plant names
        
    Raises:
        FileNotFoundError: If the file doesn't exist
        ValueError: If the file format is invalid
    """
    path = Path(file_path)
    
    if not path.exists():
        raise FileNotFoundError(f"File not found: {file_path}")
    
    plant_names = []
    
    try:
        with open(path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                
                # Skip empty lines and comments
                if not line or line.startswith('#'):
                    continue
                
                # Handle JSON format
                if line.startswith('{') or line.startswith('['):
                    try:
                        data = json.loads(line)
                        if isinstance(data, dict):
                            # Extract plant name from various possible keys
                            name = data.get('name') or data.get('plant_name') or data.get('power_plant')
                            if name:
                                plant_names.append(str(name))
                        elif isinstance(data, list):
                            plant_names.extend(str(item) for item in data)
                    except json.JSONDecodeError:
                        # Treat as plain text if JSON parsing fails
                        plant_names.append(line)
                else:
                    # Plain text format
                    plant_names.append(line)
    
    except UnicodeDecodeError:
        raise ValueError(f"File {file_path} is not valid UTF-8 text")
    
    if not plant_names:
        raise ValueError(f"No valid plant names found in {file_path}")
    
    # Remove duplicates while preserving order
    seen = set()
    unique_plants = []
    for plant in plant_names:
        if plant not in seen:
            seen.add(plant)
            unique_plants.append(plant)
    
    return unique_plants


def extract_rating_from_text(text: str) -> Optional[str]:
    """
    Extract credit rating from text using pattern matching.
    
    Args:
        text: Text to search for ratings
        
    Returns:
        Extracted rating string or None if not found
    """
    # Common rating patterns
    patterns = [
        # Moody's style: Aaa, Aa1, Aa2, etc.
        r'\b(Aaa|Aa[1-3]|A[1-3]|Baa[1-3]|Ba[1-3]|B[1-3]|Caa[1-3]|Ca|C)\b',
        
        # S&P/Fitch style: AAA, AA+, AA, AA-, etc.
        r'\b(AAA|AA[+-]?|A[+-]?|BBB[+-]?|BB[+-]?|B[+-]?|CCC[+-]?|CC|C|D)\b',
        
        # Indian agencies with similar patterns
        r'\b(LAAA|LAA[+-]?|LA[+-]?|LBBB[+-]?|LBB[+-]?|LB[+-]?|LCCC[+-]?|LCD|LD)\b',
        
        # CARE ratings
        r'\b(CARE\s+)?(AAA|AA[+-]?|A[+-]?|BBB[+-]?|BB[+-]?|B[+-]?|C|D)\b',
        
        # CRISIL ratings
        r'\b(CRISIL\s+)?(AAA|AA[+-]?|A[+-]?|BBB[+-]?|BB[+-]?|B[+-]?|C|D)\b',
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        if matches:
            # Return the first match, clean it up
            rating = matches[0]
            if isinstance(rating, tuple):
                rating = ''.join(rating)
            return rating.strip()
    
    return None


def clean_rating_string(rating: str) -> str:
    """
    Clean and standardize a rating string.
    
    Args:
        rating: Raw rating string
        
    Returns:
        Cleaned rating string
    """
    if not rating:
        return ""
    
    # Remove common prefixes
    prefixes_to_remove = ['CARE ', 'CRISIL ', 'ICRA ', 'IND ', 'BWR ']
    for prefix in prefixes_to_remove:
        if rating.upper().startswith(prefix):
            rating = rating[len(prefix):].strip()
    
    # Remove outlook indicators but keep the core rating
    outlook_indicators = [
        'Stable', 'Positive', 'Negative', 'Watch', 'Under Review', 
        'CreditWatch', 'Developing', 'Evolving', 'On Watch'
    ]
    
    for indicator in outlook_indicators:
        rating = re.sub(rf'\b{re.escape(indicator)}\b', '', rating, flags=re.IGNORECASE)
    
    # Clean up extra spaces and punctuation
    rating = re.sub(r'\s+', ' ', rating).strip()
    rating = rating.rstrip('.,;:')
    
    return rating


def extract_year_from_text(text: str) -> Optional[str]:
    """
    Extract year from text.
    
    Args:
        text: Text to search for years
        
    Returns:
        Year string or None if not found
    """
    # Look for 4-digit years between 2000-2030
    pattern = r'\b(20[0-3][0-9])\b'
    matches = re.findall(pattern, text)
    
    if matches:
        # Return the most recent year found
        years = [int(year) for year in matches]
        return str(max(years))
    
    return None


def calculate_confidence_score(
    ratings_found: int,
    sources_count: int,
    tier_used: int,
    has_direct_plant_info: bool = False
) -> float:
    """
    Calculate confidence score for search results.
    
    Args:
        ratings_found: Number of ratings found
        sources_count: Number of sources found
        tier_used: Search tier used (1-3)
        has_direct_plant_info: Whether direct plant information was found
        
    Returns:
        Confidence score between 0.0 and 1.0
    """
    score = 0.0
    
    # Base score from ratings found
    if ratings_found > 0:
        score += min(0.4, ratings_found * 0.1)
    
    # Bonus for multiple sources
    if sources_count > 1:
        score += min(0.2, (sources_count - 1) * 0.05)
    
    # Tier penalty (lower tiers are better)
    tier_multiplier = {1: 1.0, 2: 0.8, 3: 0.6}.get(tier_used, 0.5)
    score *= tier_multiplier
    
    # Bonus for direct plant information
    if has_direct_plant_info:
        score += 0.2
    
    # Ensure score is between 0 and 1
    return min(1.0, max(0.0, score))


def format_duration(seconds: float) -> str:
    """
    Format duration in a human-readable way.
    
    Args:
        seconds: Duration in seconds
        
    Returns:
        Formatted duration string
    """
    if seconds < 1:
        return f"{seconds*1000:.0f}ms"
    elif seconds < 60:
        return f"{seconds:.1f}s"
    else:
        minutes = int(seconds // 60)
        remaining_seconds = seconds % 60
        return f"{minutes}m {remaining_seconds:.1f}s"


def validate_environment() -> Dict[str, Any]:
    """
    Validate that required environment variables and dependencies are available.

    Returns:
        Dictionary with validation results
    """
    # Load environment variables from .env file
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        pass  # dotenv not available, continue with system env vars

    results = {
        "valid": True,
        "errors": [],
        "warnings": []
    }

    # Check for required environment variables
    import os
    required_env_vars = ["GEMINI_API_KEY"]
    for var in required_env_vars:
        if not os.getenv(var):
            results["valid"] = False
            results["errors"].append(f"Missing required environment variable: {var}")

    # Check for optional environment variables
    optional_env_vars = ["GOOGLE_SEARCH_API_KEY", "GOOGLE_CSE_ID"]
    for var in optional_env_vars:
        if not os.getenv(var):
            results["warnings"].append(f"Optional environment variable not set: {var}")

    return results
