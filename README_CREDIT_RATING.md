# Credit Rating Research Agent

A comprehensive standalone Python application for automatically finding and extracting credit ratings for power plants worldwide. This agent uses a sophisticated multi-tier search strategy to discover credit ratings from various agencies including CRISIL, ICRA, CARE, Moody's, S&P, and Fitch.

## Features

🔍 **Multi-Tier Search Strategy**
- **Tier 1**: Direct power plant credit rating search
- **Tier 2**: Corporate structure discovery and ownership mapping
- **Tier 3**: Parent company credit rating search as proxy

🏢 **Comprehensive Agency Coverage**
- **Indian Agencies**: CRISIL, ICRA, CARE, India Ratings, Brickwork
- **International Agencies**: Moody's, S&P Global, Fitch, Scope Ratings

📊 **Intelligent Data Processing**
- Rating standardization and outlook removal
- Temporal organization (2019-2024)
- Source attribution and confidence scoring
- Currency detection and regional mapping

💾 **Flexible Output Formats**
- Individual JSON files per power plant
- Batch processing with summary reports
- Structured data with full source attribution

## Installation

1. **Clone or download the application files**

2. **Install dependencies**:
```bash
pip install -r requirements.txt
```

3. **Set up environment variables**:
```bash
cp .env.template .env
# Edit .env and add your GEMINI_API_KEY
```

4. **Get a Gemini API Key**:
   - Visit [Google AI Studio](https://aistudio.google.com/app/apikey)
   - Create a new API key
   - Add it to your `.env` file

## Usage

### Single Power Plant Search

```bash
python main.py "Adani Mundra Power Plant"
```

### Interactive Mode

```bash
python main.py --interactive
```

### Batch Processing

```bash
python main.py --batch sample_plants.txt
```

### Advanced Options

```bash
# Custom output directory
python main.py "Plant Name" --output-dir ./custom_results/

# Verbose logging
python main.py "Plant Name" --verbose

# Custom search duration and confidence threshold
python main.py "Plant Name" --max-duration 60 --confidence-threshold 0.7

# Disable caching
python main.py "Plant Name" --no-cache
```

## Output Format

Each search generates a JSON file with the following structure:

```json
{
  "power_plant_name": "Adani Mundra Power Plant",
  "search_timestamp": "2024-01-15T10:30:00Z",
  "credit_rating": [
    {
      "agency": "CRISIL",
      "name": "CRISIL Limited",
      "yearwise_rating": [
        {
          "rating": "AA- Stable",
          "rating_trunc": "AA-",
          "year": "2023",
          "source_url": "https://example.com/rating-report",
          "instrument_type": "long-term debt"
        }
      ]
    }
  ],
  "credit_rating_note": "Detailed explanation of rating source and methodology",
  "currency": "INR",
  "level": "power_plant",
  "confidence_score": 0.85,
  "search_metadata": {
    "tier_used": 1,
    "queries_executed": ["Adani Mundra Power Plant credit rating"],
    "sources_found": 3,
    "search_duration_seconds": 12.5,
    "parent_company_identified": null
  }
}
```

## Search Strategy Details

### Tier 1: Direct Power Plant Search
Executes targeted searches with patterns like:
- `"[Power Plant Name] credit rating"`
- `"[Power Plant Name] financial rating"`
- `"[Power Plant Name] [Agency] rating"`

### Tier 2: Corporate Structure Discovery
If Tier 1 yields no results, discovers ownership with:
- `"[Power Plant Name] owner operator"`
- `"[Power Plant Name] parent company"`
- `"[Power Plant Name] subsidiary of"`

### Tier 3: Parent Company Rating Search
Uses identified parent company for:
- `"[Parent Company] credit rating [Year]"`
- `"[Parent Company] [Agency] rating"`

## Regional Agency Mapping

The agent automatically prioritizes agencies based on geographic context:

- **India**: CRISIL, ICRA, CARE (primary), India Ratings, Brickwork (secondary)
- **United States**: Moody's, S&P Global, Fitch
- **Europe/UK**: Fitch, S&P Global, Moody's, Scope Ratings

## Configuration

### Environment Variables

| Variable | Required | Description |
|----------|----------|-------------|
| `GEMINI_API_KEY` | Yes | Google Gemini API key for search and extraction |
| `GOOGLE_SEARCH_API_KEY` | No | Enhanced search capabilities |
| `GOOGLE_CSE_ID` | No | Custom Search Engine ID |
| `LOG_LEVEL` | No | Logging level (DEBUG, INFO, WARNING, ERROR) |

### Command Line Options

| Option | Default | Description |
|--------|---------|-------------|
| `--output-dir` | `./results` | Directory to save results |
| `--max-duration` | `30` | Maximum search duration per tier (seconds) |
| `--confidence-threshold` | `0.5` | Minimum confidence score for results |
| `--verbose` | `False` | Enable detailed logging |
| `--no-cache` | `False` | Disable search result caching |

## File Structure

```
credit_rating_agent/
├── main.py                    # Main application entry point
├── credit_rating_agent.py     # Core agent implementation
├── credit_rating_schemas.py   # Data models and schemas
├── search_strategies.py       # Multi-tier search strategies
├── rating_extractor.py        # Rating extraction and processing
├── utils.py                   # Utility functions
├── requirements.txt           # Python dependencies
├── .env.template             # Environment configuration template
├── sample_plants.txt         # Sample power plant list
└── README_CREDIT_RATING.md   # This documentation
```

## Examples

### Example 1: Single Plant with Verbose Output

```bash
python main.py "NTPC Vindhyachal" --verbose
```

Output:
```
🔍 Researching credit rating for: NTPC Vindhyachal
============================================================
✅ Search completed in 15.3 seconds
📊 Confidence score: 0.92
🏢 Source level: power_plant
💰 Currency: INR
🔍 Tier used: 1
📄 Sources found: 5
🏆 Ratings found from 2 agencies:
   • CRISIL Limited: 3 ratings
   • ICRA Limited: 2 ratings
💾 Results saved to: ./results/NTPC_Vindhyachal_credit_rating.json
```

### Example 2: Batch Processing

```bash
python main.py --batch sample_plants.txt --verbose
```

### Example 3: Interactive Mode

```bash
python main.py --interactive
```

```
🤖 Credit Rating Research Agent - Interactive Mode
============================================================
Enter power plant names one by one. Type 'quit' or 'exit' to stop.

🏭 Enter power plant name: Tata Power Mundra
✅ Tata Power Mundra -> ./results/Tata_Power_Mundra_credit_rating.json

🏭 Enter power plant name: quit

📊 Summary: Processed 1 power plants
👋 Goodbye!
```

## Troubleshooting

### Common Issues

1. **Missing API Key**
   ```
   Error: GEMINI_API_KEY environment variable is required
   ```
   Solution: Add your Gemini API key to the `.env` file

2. **No Results Found**
   - Check plant name spelling and try variations
   - Some plants may not have public credit ratings
   - Try searching for the parent company manually

3. **Rate Limiting**
   - The agent includes automatic retry with exponential backoff
   - Reduce search frequency if needed

### Logging

Enable verbose logging for debugging:
```bash
python main.py "Plant Name" --verbose --log-file debug.log
```

## Performance

- **Target Response Time**: <30 seconds per power plant
- **Concurrent Processing**: Sequential with progress indicators
- **Memory Usage**: Optimized for individual plant processing
- **Rate Limiting**: Compliant with API limits

## Contributing

This is a standalone application designed for the specific use case of power plant credit rating research. For enhancements or bug reports, please refer to the main project documentation.

## License

This project follows the same license as the parent LangGraph research agent project.
