# Credit Rating Research Agent - Installation Guide

This guide will help you set up and run the Credit Rating Research Agent for power plants.

## Prerequisites

- Python 3.8 or higher
- Internet connection for API access
- Google Gemini API key

## Quick Start

### 1. Download the Application

Ensure you have all the following files in your project directory:

```
credit_rating_agent/
├── main.py                    # Main application
├── credit_rating_agent.py     # Core agent
├── credit_rating_schemas.py   # Data models
├── search_strategies.py       # Search logic
├── rating_extractor.py        # Rating extraction
├── utils.py                   # Utilities
├── requirements.txt           # Dependencies
├── .env.template             # Environment template
├── sample_plants.txt         # Sample data
├── setup.py                  # Setup script
├── test_agent.py             # Test script
└── README_CREDIT_RATING.md   # Documentation
```

### 2. Run Setup Script

```bash
python setup.py
```

This will:
- Check Python version
- Install dependencies
- Create necessary directories
- Set up environment file
- Run basic tests

### 3. Configure API Key

1. Get a Gemini API key from [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Edit the `.env` file and add your key:
   ```
   GEMINI_API_KEY=your_actual_api_key_here
   ```

### 4. Test Installation

```bash
python test_agent.py
```

### 5. Run Your First Search

```bash
python main.py "Adani Mundra Power Plant"
```

## Manual Installation

If the setup script doesn't work, follow these manual steps:

### Step 1: Install Dependencies

```bash
pip install -r requirements.txt
```

Or install individually:
```bash
pip install langgraph>=0.2.6
pip install langchain>=0.3.19
pip install langchain-google-genai>=2.0.0
pip install google-genai>=0.8.0
pip install pydantic>=2.0.0
pip install python-dotenv>=1.0.1
```

### Step 2: Create Environment File

```bash
cp .env.template .env
```

Edit `.env` and add your API key:
```
GEMINI_API_KEY=your_gemini_api_key_here
```

### Step 3: Create Directories

```bash
mkdir -p results test_results logs
```

### Step 4: Test Installation

```bash
python -c "import credit_rating_schemas; print('✅ Schemas OK')"
python -c "import utils; print('✅ Utils OK')"
```

## Usage Examples

### Single Power Plant

```bash
# Basic search
python main.py "Tata Power Mundra"

# With verbose output
python main.py "NTPC Vindhyachal" --verbose

# Custom output directory
python main.py "Reliance Power Sasan" --output-dir ./my_results/
```

### Interactive Mode

```bash
python main.py --interactive
```

Then enter plant names one by one:
```
🏭 Enter power plant name: Adani Mundra Power Plant
🏭 Enter power plant name: Tata Power Mundra
🏭 Enter power plant name: quit
```

### Batch Processing

Create a file with plant names (one per line):
```bash
echo "Adani Mundra Power Plant" > my_plants.txt
echo "Tata Power Mundra" >> my_plants.txt
echo "NTPC Vindhyachal" >> my_plants.txt
```

Run batch processing:
```bash
python main.py --batch my_plants.txt
```

## Configuration Options

### Command Line Options

| Option | Default | Description |
|--------|---------|-------------|
| `--output-dir` | `./results` | Output directory |
| `--max-duration` | `30` | Max search time per tier (seconds) |
| `--confidence-threshold` | `0.5` | Minimum confidence score |
| `--verbose` | `False` | Detailed logging |
| `--no-cache` | `False` | Disable caching |
| `--log-file` | None | Log to file |

### Environment Variables

| Variable | Required | Description |
|----------|----------|-------------|
| `GEMINI_API_KEY` | Yes | Google Gemini API key |
| `LOG_LEVEL` | No | DEBUG, INFO, WARNING, ERROR |
| `MAX_REQUESTS_PER_MINUTE` | No | Rate limiting |

## Troubleshooting

### Common Issues

#### 1. Import Errors
```
ImportError: No module named 'langchain'
```
**Solution**: Install dependencies
```bash
pip install -r requirements.txt
```

#### 2. API Key Error
```
ValueError: GEMINI_API_KEY environment variable is required
```
**Solution**: Set up your API key in `.env` file

#### 3. No Results Found
```
No credit ratings found for [Plant Name]
```
**Possible causes**:
- Plant name spelling/variation
- Plant may not have public ratings
- Network connectivity issues

**Solutions**:
- Try different plant name variations
- Check internet connection
- Use `--verbose` for debugging

#### 4. Rate Limiting
```
Rate limit exceeded
```
**Solution**: The agent has built-in retry logic, but you can:
- Reduce search frequency
- Wait and retry
- Check API quota

### Getting Help

1. **Check logs**: Use `--verbose` and `--log-file` options
2. **Test components**: Run `python test_agent.py`
3. **Validate environment**: Check `.env` file setup
4. **Try sample data**: Use `sample_plants.txt` for testing

### Performance Tips

1. **Use caching**: Keep `--no-cache` disabled (default)
2. **Adjust timeouts**: Use `--max-duration` for faster/slower searches
3. **Batch processing**: More efficient for multiple plants
4. **Monitor confidence**: Adjust `--confidence-threshold` as needed

## Output Format

Results are saved as JSON files with this structure:

```json
{
  "power_plant_name": "Plant Name",
  "search_timestamp": "2024-01-15T10:30:00Z",
  "credit_rating": [
    {
      "agency": "CRISIL",
      "name": "CRISIL Limited",
      "yearwise_rating": [
        {
          "rating": "AA- Stable",
          "rating_trunc": "AA-",
          "year": "2023",
          "source_url": "https://example.com"
        }
      ]
    }
  ],
  "credit_rating_note": "Explanation of findings",
  "currency": "INR",
  "level": "power_plant",
  "confidence_score": 0.85,
  "search_metadata": {
    "tier_used": 1,
    "queries_executed": ["search queries"],
    "sources_found": 3,
    "search_duration_seconds": 12.5
  }
}
```

## Next Steps

After successful installation:

1. **Test with known plants**: Start with major plants like "Adani Mundra Power Plant"
2. **Explore options**: Try different command-line options
3. **Batch processing**: Process multiple plants efficiently
4. **Analyze results**: Review confidence scores and source attribution
5. **Customize**: Adjust configuration for your specific needs

For detailed usage instructions, see `README_CREDIT_RATING.md`.
